
import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Link, useLocation } from 'react-router-dom'
import { gsap } from 'gsap'

const Navbar = () => {
  const [scrollState, setScrollState] = useState({
    isVisible: false,
    isCompact: false,
    scrollY: 0
  })
  const location = useLocation()
  const navbarRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      setScrollState({
        isVisible: scrollY > 300,
        isCompact: scrollY > 800,
        scrollY
      })
    }

    // Initial animation - hide navbar
    if (navbarRef.current) {
      gsap.set(navbarRef.current, { y: -100, opacity: 0 })
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Check initial scroll position
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Handle navbar animation based on scroll state
  useEffect(() => {
    if (!navbarRef.current) return

    if (scrollState.isVisible) {
      gsap.to(navbarRef.current, {
        y: 0,
        opacity: 1,
        duration: 0.4,
        ease: 'power2.out'
      })
    } else {
      gsap.to(navbarRef.current, {
        y: -100,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in'
      })
    }
  }, [scrollState.isVisible])

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'What We Do', path: '/services' },
    { name: 'Our Pricing', path: '/pricing' },
    { name: 'How We Work', path: '/process' },
    { name: 'Case Studies', path: '/work' },
    { name: 'About Us', path: '/about' }
  ]

  return (
    <header
      ref={navbarRef}
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300 w-full hidden md:block',
        scrollState.isVisible ? 'opacity-100' : 'opacity-0 -translate-y-full',
        scrollState.isCompact
          ? 'py-2 mx-auto px-6 mt-2 max-w-[95%] rounded-full'
          : 'py-4 mx-auto px-4 mt-4 max-w-[98%] rounded-xl',
        'bg-agency-darker bg-opacity-95 shadow-lg backdrop-blur-sm'
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center">
            <img
              src="/logo-01.svg"
              alt="KavaraDigital"
              className={cn(
                'transition-all duration-300',
                scrollState.isCompact ? 'w-40 h-8' : 'w-50 h-12'
              )}
            />
          </Link>

          <nav className="flex items-center space-x-6">
            {navItems.map((item) => {
              const isNavItemActive = isActive(item.path)
              return (
                <Link
                  key={item.name}
                  to={item.path}
                  className={cn(
                    'transition-all duration-200',
                    scrollState.isCompact ? 'text-xs' : 'text-sm',
                    isNavItemActive
                      ? 'text-agency-green font-medium'
                      : 'text-agency-white-muted hover:text-agency-green'
                  )}
                >
                  {item.name}
                </Link>
              )
            })}
          </nav>

          <Link
            to="/contact"
            className={cn(
              'px-4 py-2 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow',
              scrollState.isCompact ? 'text-xs py-1.5' : 'text-sm py-2'
            )}
          >
            Contact Us
          </Link>
        </div>
      </div>
    </header>
  )
}

export default Navbar;
